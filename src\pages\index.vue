<script setup lang="ts">
import { useRoute } from 'vue-router'
import { analyzeApi } from '~/api/analyze'
import { productLineApi } from '~/api/line'
import type { ProductLine } from '~/api/line/types'
import LineContainer from '~/components/oee/LineContainer.vue'
import { useAggregationStore } from '~/stores/aggregation'
import Honghong from '~/assets/honghong.gif'

const startTime = ref<Date>()
const endTime = ref<Date>()
const route = useRoute()
const loading = ref<boolean>(false)
const aggregationStore = useAggregationStore()
const workshopCode = ref<string>()
const refreshInterval = ref<ReturnType<typeof setInterval>>()
const el = useTemplateRef<HTMLElement>('el')
const { width, height } = useWindowSize()
const { style } = useDraggable(el, {
  initialValue: { x: width.value * 0.01, y: height.value * 0.75 },
})

// 数据状态
const lineList = ref<ProductLine[]>([])
const show = ref<boolean>(false)

// 加载数据
async function loadData(code: string) {
  loading.value = true
  try {
    const res = await productLineApi.listByWorkShopId(code)
    lineList.value = res
  }
  finally {
    loading.value = false
  }
}

// 日期处理方法
async function initializeDates() {
  try {
    const serverTime = await analyzeApi.getServerTime()
    const now = new Date(serverTime)
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
  catch (error) {
    console.error('Failed to get server time:', error)
    // 使用当前时间
    const now = new Date()
    const currentDay = new Date(now)
    const nextDay = new Date(now)
    nextDay.setDate(now.getDate() + 1)

    startTime.value = new Date(currentDay.setHours(8, 0, 0, 0))
    endTime.value = new Date(nextDay.setHours(8, 0, 0, 0))
  }
}

function checkAndUpdateDates() {
  const now = new Date()
  if (!endTime.value || now > endTime.value) {
    initializeDates()
  }
}

watch(() => route.params.code, (newCode) => {
  if (newCode) {
    const code = Array.isArray(route.params.code) ? route.params.code[0] : route.params.code
    workshopCode.value = code
    initializeDates()
    loadData(code)
  }
}, { immediate: true })

onBeforeMount(() => {
  // 初始化日期
  // initializeDates()
  // // 搜索
  // if (workshopCode.value)
  //   loadData(workshopCode.value)
  // 设置自动刷新
  refreshInterval.value = setInterval(() => {
    checkAndUpdateDates()
    if (workshopCode.value)
      loadData(workshopCode.value)
  }, 300000) // 5分钟刷新一次
})

onBeforeUnmount(() => {
  // 清除自动刷新
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})

// 重置时间
function resetDates() {
  initializeDates()
}
</script>

<template>
  <div class="dashboard-screen" :class="{ light: !isDark, dark: isDark }">
    <!-- 背景装饰 -->
    <div class="dashboard-bg">
      <div class="bg-grid" />
      <div class="bg-glow" />
    </div>

    <!-- 浮动Logo -->
    <img v-if="show" ref="el" :src="Honghong" alt="Logo" class="floating-logo" :style="style">

    <!-- 控制面板 -->
    <div v-show="aggregationStore.isSearchVisible" class="control-panel">
      <div class="control-group">
        <DatePicker
          v-model="startTime"
          fluid
          show-icon
          date-format="yy-mm-dd"
          v-bind="{ showTime: true, hourFormat: '24' }"
          class="date-picker-enhanced"
        >
          <template #inputicon="slotProps">
            <i class="pi pi-clock" @click="slotProps.clickCallback" />
          </template>
        </DatePicker>
        <DatePicker
          v-model="endTime"
          fluid
          show-icon
          date-format="yy-mm-dd"
          v-bind="{ showTime: true, hourFormat: '24' }"
          class="date-picker-enhanced"
        >
          <template #inputicon="slotProps">
            <i class="pi pi-clock" @click="slotProps.clickCallback" />
          </template>
        </DatePicker>
        <Button
          size="small"
          icon="pi pi-refresh"
          class="refresh-btn"
          severity="secondary"
          @click="resetDates"
        />
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="dashboard-content">
      <div class="line-container-wrapper">
        <LineContainer
          v-for="item in lineList"
          :key="item.code"
          :code="item.code"
          :start-time="startTime!"
          :end-time="endTime!"
          class="line-item"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-screen {
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  font-family:
    'Noto Sans',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
}

.dashboard-screen.light {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
}

/* 背景装饰 */
.dashboard-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

.light .bg-grid {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
}

.bg-glow {
  position: absolute;
  top: 20%;
  left: 20%;
  width: 60%;
  height: 60%;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  animation: glow-pulse 4s ease-in-out infinite alternate;
}

.light .bg-glow {
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
}

/* 浮动Logo */
.floating-logo {
  position: fixed;
  z-index: 999;
  width: 8rem;
  cursor: grab;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.floating-logo:hover {
  transform: scale(1.05);
}

/* 控制面板 */
.control-panel {
  position: relative;
  z-index: 10;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.control-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.75rem;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
}

.light .control-group {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.date-picker-enhanced {
  min-width: 200px;
}

.refresh-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: rotate(180deg);
}

/* 数据展示区域 */
.dashboard-content {
  position: relative;
  z-index: 5;
  height: calc(100vh - 4rem);
  padding: 0 0.5rem 0.75rem;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
}

/* 自定义滚动条 */
.dashboard-content::-webkit-scrollbar {
  width: 8px;
}

.dashboard-content::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

.dashboard-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.6) 0%, rgba(147, 51, 234, 0.6) 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.8) 0%, rgba(147, 51, 234, 0.8) 100%);
}

.light .dashboard-content::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.8);
}

.light .dashboard-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.4) 0%, rgba(147, 51, 234, 0.4) 100%);
}

.light .dashboard-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.6) 0%, rgba(147, 51, 234, 0.6) 100%);
}

.line-container-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.line-item {
  animation: slide-in-up 0.6s ease-out;
  animation-fill-mode: both;
}

.line-item:nth-child(1) {
  animation-delay: 0.1s;
}
.line-item:nth-child(2) {
  animation-delay: 0.2s;
}
.line-item:nth-child(3) {
  animation-delay: 0.3s;
}
.line-item:nth-child(4) {
  animation-delay: 0.4s;
}
.line-item:nth-child(5) {
  animation-delay: 0.5s;
}

/* 动画效果 */
@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes glow-pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    padding: 1rem;
  }

  .control-group {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .date-picker-enhanced {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 0 0.25rem 0.5rem;
  }

  .line-container-wrapper {
    gap: 0.75rem;
  }

  .control-panel {
    padding: 0.75rem 0.5rem;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
    padding: 0.625rem;
  }

  .date-picker-enhanced {
    min-width: auto;
  }
}

/* 全局防止横向滚动 */
* {
  box-sizing: border-box;
}

html,
body {
  overflow-x: hidden;
  max-width: 100%;
}
</style>
