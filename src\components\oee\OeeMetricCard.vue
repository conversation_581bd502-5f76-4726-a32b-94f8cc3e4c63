<script setup lang="ts">
const props = defineProps<{
  title: string
  targetValue: string | number
  actualValue: string | number
}>()

// 计算达成率和状态
const achievementRate = computed(() => {
  const target = Number(props.targetValue)
  const actual = Number(props.actualValue)
  if (target === 0)
    return 0
  return Math.round((actual / target) * 100)
})

const performanceStatus = computed(() => {
  const rate = achievementRate.value
  if (rate >= 100)
    return 'excellent'
  if (rate >= 90)
    return 'good'
  if (rate >= 80)
    return 'warning'
  return 'poor'
})

const statusColor = computed(() => {
  switch (performanceStatus.value) {
    case 'excellent': return '#10b981' // green
    case 'good': return '#3b82f6' // blue
    case 'warning': return '#f59e0b' // amber
    case 'poor': return '#ef4444' // red
    default: return '#6b7280' // gray
  }
})
</script>

<template>
  <div class="oee-metric-card" :class="`status-${performanceStatus}`">
    <div class="card-header">
      <span class="metric-title">{{ title }}</span>
      <!-- <div class="achievement-badge" :style="{ backgroundColor: statusColor }">
        {{ achievementRate }}%
      </div> -->
    </div>

    <div class="values-section">
      <div class="value-item">
        <span class="value-label">目标</span>
        <span class="value-number target">{{ targetValue }}%</span>
      </div>
      <div class="value-divider" />
      <div class="value-item">
        <span class="value-label">实际</span>
        <span class="value-number actual" :style="{ color: statusColor }">{{ actualValue }}%</span>
      </div>
    </div>

    <div class="progress-bar">
      <div
        class="progress-fill"
        :style="{
          width: `${Math.min(achievementRate, 100)}%`,
          backgroundColor: statusColor,
        }"
      />
    </div>

    <div class="metric-glow" :style="{ backgroundColor: statusColor }" />
  </div>
</template>

<style scoped>
.oee-metric-card {
  position: relative;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 80px;
  transition: all 0.3s ease;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.oee-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
}

.oee-metric-card:hover {
  transform: translateY(-3px);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow:
    0 15px 35px -5px rgba(0, 0, 0, 0.15),
    0 0 20px rgba(59, 130, 246, 0.2);
}

.oee-metric-card:hover .metric-glow {
  opacity: 0.3;
}

/* 状态特定样式 */
.status-excellent {
  border-color: rgba(16, 185, 129, 0.3);
}

.status-good {
  border-color: rgba(59, 130, 246, 0.3);
}

.status-warning {
  border-color: rgba(245, 158, 11, 0.3);
}

.status-poor {
  border-color: rgba(239, 68, 68, 0.3);
}

/* 亮色模式 */
:global(.light) .oee-metric-card {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

:global(.light) .oee-metric-card:hover {
  box-shadow:
    0 15px 35px -5px rgba(0, 0, 0, 0.08),
    0 0 20px rgba(59, 130, 246, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.metric-title {
  font-size: 1rem;
  color: rgba(241, 245, 249, 0.95);
  font-weight: 600;
  flex: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

:global(.light) .metric-title {
  color: rgba(51, 65, 85, 0.9);
  text-shadow: none;
}

.achievement-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
  min-width: 50px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.values-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
}

.value-label {
  font-size: 0.8rem;
  color: rgba(203, 213, 225, 0.85);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

:global(.light) .value-label {
  color: rgba(71, 85, 105, 0.8);
  text-shadow: none;
}

.value-number {
  font-size: 1.125rem;
  font-weight: 700;
  text-align: center;
}

.target {
  color: rgba(226, 232, 240, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

:global(.light) .target {
  color: rgba(71, 85, 105, 0.85);
  text-shadow: none;
}

.value-divider {
  width: 1px;
  height: 30px;
  background: rgba(59, 130, 246, 0.3);
  flex-shrink: 0;
}

.progress-bar {
  position: relative;
  height: 6px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.8s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

.metric-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  filter: blur(20px);
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .oee-metric-card {
    padding: 1rem;
    min-height: 120px;
  }

  .metric-title {
    font-size: 0.9rem;
  }

  .value-number {
    font-size: 1.05rem;
  }

  .achievement-badge {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .oee-metric-card {
    padding: 0.75rem;
    min-height: 100px;
  }

  .values-section {
    gap: 0.5rem;
  }

  .metric-title {
    font-size: 0.85rem;
  }

  .value-number {
    font-size: 1rem;
  }
}
</style>
